package com.yzedulife.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.yzedulife.common.domain.BusinessException;
import com.yzedulife.service.dto.AnswerSheetDTO;
import com.yzedulife.service.dto.QuestionnaireDTO;
import com.yzedulife.service.dto.StudentUserDTO;
import com.yzedulife.service.service.AnswerSheetService;
import com.yzedulife.service.service.QuestionnaireService;
import com.yzedulife.service.service.StudentUserService;
import com.yzedulife.vo.AnswerSheetVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDateTime;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(AnswerController.class)
public class AnswerControllerCreateTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private AnswerSheetService answerSheetService;

    @MockBean
    private QuestionnaireService questionnaireService;

    @MockBean
    private StudentUserService studentUserService;

    private AnswerSheetVO testAnswerSheetVO;
    private AnswerSheetDTO testAnswerSheetDTO;
    private QuestionnaireDTO testQuestionnaireDTO;
    private StudentUserDTO testStudentUserDTO;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        testAnswerSheetVO = new AnswerSheetVO();
        testAnswerSheetVO.setQuestionnaireId(1L);

        testAnswerSheetDTO = new AnswerSheetDTO();
        testAnswerSheetDTO.setId(1L);
        testAnswerSheetDTO.setQuestionnaireId(1L);
        testAnswerSheetDTO.setSubmitterType("STUDENT");
        testAnswerSheetDTO.setStudentUserId(1L);
        testAnswerSheetDTO.setSubmitTime(LocalDateTime.now());

        testQuestionnaireDTO = new QuestionnaireDTO();
        testQuestionnaireDTO.setId(1L);
        testQuestionnaireDTO.setTitle("测试问卷");

        testStudentUserDTO = new StudentUserDTO();
        testStudentUserDTO.setId(1L);
        testStudentUserDTO.setName("测试学生");
    }

    @Test
    @DisplayName("创建答卷 - 成功")
    void create_Success() throws Exception {
        // Given
        when(questionnaireService.getById(1L)).thenReturn(testQuestionnaireDTO);
        when(studentUserService.getById(1L)).thenReturn(testStudentUserDTO);
        when(answerSheetService.create(any(AnswerSheetDTO.class))).thenReturn(testAnswerSheetDTO);

        // When & Then
        mockMvc.perform(post("/answer/create")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(testAnswerSheetVO))
                .header("Authorization", "Bearer test-token"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("答卷创建成功"))
                .andExpect(jsonPath("$.data.id").value(1))
                .andExpect(jsonPath("$.data.questionnaireId").value(1));
    }

    @Test
    @DisplayName("创建答卷 - 问卷不存在")
    void create_QuestionnaireNotFound() throws Exception {
        // Given
        when(questionnaireService.getById(1L)).thenReturn(null);

        // When & Then
        mockMvc.perform(post("/answer/create")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(testAnswerSheetVO))
                .header("Authorization", "Bearer test-token"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("问卷不存在"));
    }

    @Test
    @DisplayName("创建答卷 - 业务异常")
    void create_BusinessException() throws Exception {
        // Given
        when(questionnaireService.getById(1L)).thenReturn(testQuestionnaireDTO);
        when(studentUserService.getById(1L)).thenReturn(testStudentUserDTO);
        when(answerSheetService.create(any(AnswerSheetDTO.class)))
                .thenThrow(new BusinessException("该学生已经答过此问卷"));

        // When & Then
        mockMvc.perform(post("/answer/create")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(testAnswerSheetVO))
                .header("Authorization", "Bearer test-token"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("该学生已经答过此问卷"));
    }
}
