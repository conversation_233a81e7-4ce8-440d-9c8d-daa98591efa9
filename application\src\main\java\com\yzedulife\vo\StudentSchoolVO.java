package com.yzedulife.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 学生学校VO
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class StudentSchoolVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 学校ID
     */
    private Long id;

    /**
     * 学校名称
     */
    private String schoolName;
}
