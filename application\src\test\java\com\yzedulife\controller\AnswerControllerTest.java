package com.yzedulife.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.yzedulife.common.domain.BusinessException;
import com.yzedulife.service.dto.*;
import com.yzedulife.service.service.*;
import com.yzedulife.util.MockUtil;
import com.yzedulife.util.TestDataFactory;
import com.yzedulife.vo.AnswerDetailVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(AnswerController.class)
@ActiveProfiles("test")
@DisplayName("答案控制器测试")
class AnswerControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private AnswerSheetService answerSheetService;

    @MockBean
    private AnswerDetailService answerDetailService;

    @MockBean
    private QuestionOptionService questionOptionService;

    @MockBean
    private StudentUserService studentUserService;

    @MockBean
    private OtherUserService otherUserService;

    @MockBean
    private QuestionnaireService questionnaireService;

    private QuestionnaireDTO testQuestionnaire;
    private AnswerSheetDTO testAnswerSheet;
    private List<AnswerDetailVO> testAnswerDetails;
    private List<AnswerSheetDTO> testAnswerSheetList;

    @BeforeEach
    void setUp() {
        testQuestionnaire = TestDataFactory.createQuestionnaireDTO();
        testAnswerSheet = TestDataFactory.createAnswerSheetDTO();
        testAnswerDetails = TestDataFactory.createAnswerDetailVOList();
        
        testAnswerSheetList = new ArrayList<>();
        testAnswerSheetList.add(testAnswerSheet);
        
        AnswerSheetDTO answerSheet2 = TestDataFactory.createAnswerSheetDTO();
        answerSheet2.setId(2L);
        answerSheet2.setSubmitterType("SOCIAL");
        answerSheet2.setStudentUserId(null);
        answerSheet2.setOtherUserId(1L);
        testAnswerSheetList.add(answerSheet2);
    }

    @Test
    @DisplayName("提交答案 - 学生用户成功")
    void submit_StudentSuccess() throws Exception {
        // Given
        when(questionnaireService.getById(1L)).thenReturn(testQuestionnaire);
        when(answerSheetService.create(any(AnswerSheetDTO.class))).thenReturn(testAnswerSheet);
        List<QuestionOptionDTO> options1 = new ArrayList<>();
        QuestionOptionDTO option1 = TestDataFactory.createQuestionOptionDTO();
        option1.setOptionCode("A");
        options1.add(option1);
        when(questionOptionService.getByQuestionId(1L)).thenReturn(options1);

        List<QuestionOptionDTO> options2 = new ArrayList<>();
        QuestionOptionDTO option2 = TestDataFactory.createQuestionOptionDTO();
        option2.setOptionCode("B");
        options2.add(option2);
        when(questionOptionService.getByQuestionId(2L)).thenReturn(options2);
        when(answerDetailService.create(any(AnswerDetailDTO.class)))
                .thenReturn(TestDataFactory.createAnswerDetailDTO());

        // When & Then
        mockMvc.perform(MockUtil.postWithStudentToken("/answer/submit")
                        .param("questionnaireId", "1")
                        .content(MockUtil.asJsonString(testAnswerDetails)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.msg").value("答案提交成功"));

        verify(questionnaireService).getById(1L);
        verify(answerSheetService).create(any(AnswerSheetDTO.class));
        verify(answerDetailService, times(2)).create(any(AnswerDetailDTO.class));
    }

    @Test
    @DisplayName("提交答案 - 社会人士用户成功")
    void submit_OtherUserSuccess() throws Exception {
        // Given
        when(questionnaireService.getById(1L)).thenReturn(testQuestionnaire);
        when(answerSheetService.create(any(AnswerSheetDTO.class))).thenReturn(testAnswerSheet);
        List<QuestionOptionDTO> options = new ArrayList<>();
        QuestionOptionDTO option = TestDataFactory.createQuestionOptionDTO();
        option.setOptionCode("A");
        options.add(option);
        when(questionOptionService.getByQuestionId(anyLong())).thenReturn(options);
        when(answerDetailService.create(any(AnswerDetailDTO.class)))
                .thenReturn(TestDataFactory.createAnswerDetailDTO());

        // When & Then
        mockMvc.perform(MockUtil.postWithOtherToken("/answer/submit")
                        .param("questionnaireId", "1")
                        .content(MockUtil.asJsonString(testAnswerDetails)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.msg").value("答案提交成功"));

        verify(questionnaireService).getById(1L);
        verify(answerSheetService).create(any(AnswerSheetDTO.class));
        verify(answerDetailService, times(2)).create(any(AnswerDetailDTO.class));
    }

    @Test
    @DisplayName("提交答案 - 问卷不存在")
    void submit_QuestionnaireNotFound() throws Exception {
        // Given
        when(questionnaireService.getById(999L))
                .thenThrow(new BusinessException("问卷不存在"));

        // When & Then
        mockMvc.perform(MockUtil.postWithStudentToken("/answer/submit")
                        .param("questionnaireId", "999")
                        .content(MockUtil.asJsonString(testAnswerDetails)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.msg").value("问卷不存在"));

        verify(questionnaireService).getById(999L);
        verify(answerSheetService, never()).create(any());
        verify(answerDetailService, never()).create(any());
    }

    @Test
    @DisplayName("提交答案 - 答案列表为空")
    void submit_EmptyAnswerList() throws Exception {
        // Given
        when(questionnaireService.getById(1L)).thenReturn(testQuestionnaire);

        // When & Then
        mockMvc.perform(MockUtil.postWithStudentToken("/answer/submit")
                        .param("questionnaireId", "1")
                        .content(MockUtil.asJsonString(new ArrayList<>())))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.msg").value("答案不能为空"));

        verify(questionnaireService).getById(1L);
        verify(answerSheetService, never()).create(any());
        verify(answerDetailService, never()).create(any());
    }

    @Test
    @DisplayName("提交答案 - 选项不存在")
    void submit_OptionNotFound() throws Exception {
        // Given
        when(questionnaireService.getById(1L)).thenReturn(testQuestionnaire);
        when(answerSheetService.create(any(AnswerSheetDTO.class))).thenReturn(testAnswerSheet);
        when(questionOptionService.getByQuestionId(1L))
                .thenReturn(new ArrayList<>()); // 返回空列表表示选项不存在

        // When & Then
        mockMvc.perform(MockUtil.postWithStudentToken("/answer/submit")
                        .param("questionnaireId", "1")
                        .content(MockUtil.asJsonString(testAnswerDetails)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.msg").value("题目ID 1 的选项代号 A 不存在"));

        verify(questionnaireService).getById(1L);
        verify(answerSheetService).create(any(AnswerSheetDTO.class));
        verify(questionOptionService).getByQuestionId(1L);
        verify(answerDetailService, never()).create(any());
    }

    @Test
    @DisplayName("提交答案 - 无权限")
    void submit_NoPermission() throws Exception {
        // When & Then
        mockMvc.perform(MockUtil.postWithAdminToken("/answer/submit")
                        .param("questionnaireId", "1")
                        .content(MockUtil.asJsonString(testAnswerDetails)))
                .andDo(print())
                .andExpect(status().isForbidden());

        verify(questionnaireService, never()).getById(any());
        verify(answerSheetService, never()).create(any());
    }

    @Test
    @DisplayName("查看答案 - 管理员未指定问卷ID，返回所有答卷")
    void list_AdminWithoutQuestionnaireId() throws Exception {
        // Given
        when(answerSheetService.getAll()).thenReturn(testAnswerSheetList);

        // When & Then
        mockMvc.perform(MockUtil.getWithAdminToken("/answer/list"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(2));

        verify(answerSheetService).getAll();
        verify(answerSheetService, never()).getByQuestionnaireId(any());
    }

    @Test
    @DisplayName("查看答案 - 管理员查看指定问卷答卷")
    void list_AdminViewByQuestionnaire() throws Exception {
        // Given
        when(answerSheetService.getByQuestionnaireId(1L)).thenReturn(testAnswerSheetList);

        // When & Then
        mockMvc.perform(MockUtil.getWithAdminToken("/answer/list")
                        .param("questionnaireId", "1"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(2));

        verify(answerSheetService).getByQuestionnaireId(1L);
    }

    @Test
    @DisplayName("查看答案 - 业务异常")
    void list_BusinessException() throws Exception {
        // Given
        when(answerSheetService.getByQuestionnaireId(1L))
                .thenThrow(new BusinessException("获取答卷列表失败"));

        // When & Then
        mockMvc.perform(MockUtil.getWithAdminToken("/answer/list")
                        .param("questionnaireId", "1"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.msg").value("获取答卷列表失败"));

        verify(answerSheetService).getByQuestionnaireId(1L);
    }

    @Test
    @DisplayName("查看答案 - 无权限")
    void list_NoPermission() throws Exception {
        // When & Then
        mockMvc.perform(MockUtil.getWithStudentToken("/answer/list"))
                .andDo(print())
                .andExpect(status().isForbidden());

        verify(answerSheetService, never()).getByQuestionnaireId(any());
    }

    @Test
    @DisplayName("查看答案详情 - 成功")
    void detail_Success() throws Exception {
        // Given
        when(answerSheetService.getById(1L)).thenReturn(testAnswerSheet);
        when(studentUserService.getById(1L)).thenReturn(TestDataFactory.createStudentUserDTO());
        
        List<AnswerDetailDTO> answerDetails = new ArrayList<>();
        AnswerDetailDTO detail = TestDataFactory.createAnswerDetailDTO();
        answerDetails.add(detail);
        when(answerDetailService.getByAnswerSheetId(1L)).thenReturn(answerDetails);
        
        // 不再需要mock questionOptionService.getById，因为答案详情直接存储选项代号

        // When & Then
        mockMvc.perform(MockUtil.getWithAdminToken("/answer/detail")
                        .param("id", "1"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.id").value(1))
                .andExpect(jsonPath("$.data.submitterType").value("STUDENT"))
                .andExpect(jsonPath("$.data.answerDetails").isArray());

        verify(answerSheetService).getById(1L);
        verify(studentUserService).getById(1L);
        verify(answerDetailService).getByAnswerSheetId(1L);
        // 不再需要verify questionOptionService.getById
    }

    @Test
    @DisplayName("查看答案详情 - 答卷不存在")
    void detail_AnswerSheetNotFound() throws Exception {
        // Given
        when(answerSheetService.getById(999L))
                .thenThrow(new BusinessException("答卷不存在"));

        // When & Then
        mockMvc.perform(MockUtil.getWithAdminToken("/answer/detail")
                        .param("id", "999"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.msg").value("答卷不存在"));

        verify(answerSheetService).getById(999L);
        verify(studentUserService, never()).getById(any());
        verify(answerDetailService, never()).getByAnswerSheetId(any());
    }

    @Test
    @DisplayName("查看答案详情 - 无权限")
    void detail_NoPermission() throws Exception {
        // When & Then
        mockMvc.perform(MockUtil.getWithStudentToken("/answer/detail")
                        .param("id", "1"))
                .andDo(print())
                .andExpect(status().isForbidden());

        verify(answerSheetService, never()).getById(any());
    }
}
