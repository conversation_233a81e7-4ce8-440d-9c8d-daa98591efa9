package com.yzedulife.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 答卷响应
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Schema(description = "答卷响应")
public class AnswerSheetResponse {
    
    @Schema(description = "答卷ID")
    private Long id;
    
    @Schema(description = "问卷ID")
    private Long questionnaireId;
    
    @Schema(description = "问卷标题")
    private String questionnaireTitle;
    
    @Schema(description = "提交者类型", example = "STUDENT 或 SOCIAL")
    private String submitterType;
    
    @Schema(description = "学生用户ID")
    private Long studentUserId;
    
    @Schema(description = "学生姓名")
    private String studentName;

    @Schema(description = "学号")
    private String studentNumber;

    @Schema(description = "班级ID")
    private Long classId;

    @Schema(description = "班级名称")
    private String className;

    @Schema(description = "社会人士用户ID")
    private Long otherUserId;
    
    @Schema(description = "手机号")
    private String phone;
    
    @Schema(description = "提交时间")
    private LocalDateTime submitTime;

    @Schema(description = "答案列表")
    private List<AnswerDetailResponse> answers;
}
